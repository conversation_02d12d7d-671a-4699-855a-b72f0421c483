"use client"
import { useState } from 'react';
import SearchTabs from '../ui/SearchTabs';
import SearchInput from '../ui/SearchInput';
import PromotionalBanner from '../ui/PromotionalBanner';

const HeroSection = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <section className="relative pt-24 pb-20 bg-white">
      {/* Main Hero Content */}
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Title */}
        <div className="text-center mb-16">
          <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-gray-900 mb-12">
            Where to?
          </h1>
        </div>

        {/* Search Interface */}
        <div className="bg-white">
          <SearchTabs activeTab={activeTab} onTabChange={setActiveTab} />
          <div className="mt-8">
            <SearchInput searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;