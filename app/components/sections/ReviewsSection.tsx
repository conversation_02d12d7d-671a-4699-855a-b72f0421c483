'use client';

import { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import ReviewCard from '../ui/ReviewCard';
import TrustIndicators from '../ui/TrustIndicators';
import CarouselNavigation from '../ui/CarouselNavigation';

const ReviewsSection = () => {
  const [currentReview, setCurrentReview] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const reviews = [
    {
      id: 1,
      name: "<PERSON>",
      location: "London, UK",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b932?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      date: "March 2024",
      tour: "3-Day Sahara Desert Trek",
      review: "Absolutely incredible experience! The guides were knowledgeable and friendly, and the desert sunset was breathtaking. The camel ride and overnight stay in the traditional Berber camp was unforgettable. Highly recommend OurikaTravels for anyone wanting an authentic Moroccan adventure!",
      images: [
        "https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
        "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      ],
      verified: true
    },
    {
      id: 2,
      name: "Ahmed El-Mansouri",
      location: "Paris, France",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      date: "February 2024",
      tour: "Ouzoud Waterfalls Day Trip",
      review: "Perfect day trip from Marrakech! The waterfalls were stunning and our guide Youssef was amazing. He showed us the best spots for photos and taught us about local wildlife. The boat ride was a nice touch. Great value for money!",
      images: [
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      ],
      verified: true
    },
    {
      id: 3,
      name: "Maria Rodriguez",
      location: "Barcelona, Spain",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      date: "January 2024",
      tour: "Atlas Mountains Toubkal Trek",
      review: "Challenging but rewarding trek to Mount Toubkal! The mountain guides were professional and safety-focused. The views from the summit were absolutely spectacular. The accommodation in the mountain refuges was better than expected. Will definitely book with OurikaTravels again!",
      images: [
        "https://images.unsplash.com/photo-1464822759844-d150baec58d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      ],
      verified: true
    },
    {
      id: 4,
      name: "David Thompson",
      location: "New York, USA",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      date: "December 2023",
      tour: "Ourika Valley Cultural Tour",
      review: "Fantastic cultural immersion! We visited traditional Berber villages, enjoyed mint tea with locals, and learned about traditional crafts. The lunch in the valley was delicious. Our guide spoke excellent English and was very informative about Moroccan culture and history.",
      images: [
        "https://images.unsplash.com/photo-1539650116574-75c0c6d73d0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      ],
      verified: true
    },
    {
      id: 5,
      name: "Emma Wilson",
      location: "Melbourne, Australia",
      avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80",
      rating: 5,
      date: "November 2023",
      tour: "Marrakech Food & Souks Tour",
      review: "Amazing food tour through the medina! We tried so many delicious local dishes and learned about spices and cooking techniques. The souk navigation was perfect - our guide knew all the best hidden spots. Great mix of food, culture, and shopping!",
      images: [
        "https://images.unsplash.com/photo-1539650116574-75c0c6d73d0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
        "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      ],
      verified: true
    }
  ];

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, reviews.length]);

  const nextReview = () => {
    setCurrentReview((prev) => (prev + 1) % reviews.length);
    setIsAutoPlaying(false);
  };

  const prevReview = () => {
    setCurrentReview((prev) => (prev - 1 + reviews.length) % reviews.length);
    setIsAutoPlaying(false);
  };

  const goToReview = (index: number) => {
    setCurrentReview(index);
    setIsAutoPlaying(false);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  const currentReviewData = reviews[currentReview];

  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-gray-50">
      <div className="w-full mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="flex">{renderStars(5)}</div>
            <span className="text-2xl font-bold text-gray-900">4.9</span>
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            What our travelers say
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Over 50,000+ verified reviews from travelers who experienced the magic of Morocco with us
          </p>
        </div>

        {/* Main Review Carousel */}
        <div className="relative bg-white rounded-3xl shadow-xl overflow-hidden mb-12">
          <div className="relative">
            {/* Review Content */}
            <ReviewCard review={currentReviewData} />

            {/* Navigation */}
            <CarouselNavigation
              currentIndex={currentReview}
              totalItems={reviews.length}
              onPrevious={prevReview}
              onNext={nextReview}
              onGoTo={goToReview}
            />
          </div>
        </div>

        {/* Trust Indicators */}
        <TrustIndicators />
      </div>
    </section>
  );
};

export default ReviewsSection;