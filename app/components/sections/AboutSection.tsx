'use client';

import { useState, useEffect } from 'react';
import { Award, Users, Heart, Leaf } from 'lucide-react';
import AnimatedCounter from '../ui/AnimatedCounter';

const AboutSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const section = document.getElementById('about-section');
    if (section) {
      observer.observe(section);
    }

    return () => observer.disconnect();
  }, []);

  const stats = [
    { number: 15, suffix: '+', label: 'Years Experience', icon: Award },
    { number: 50000, suffix: '+', label: 'Happy Travelers', icon: Users },
    { number: 98, suffix: '%', label: 'Satisfaction Rate', icon: Heart },
    { number: 25, suffix: '+', label: 'Local Partners', icon: Leaf }
  ];

  return (
    <section id="about-section" className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-gray-50 via-white to-emerald-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 border-2 border-emerald-500 rounded-full"></div>
        <div className="absolute top-40 right-20 w-24 h-24 border-2 border-orange-400 rounded-lg rotate-45"></div>
        <div className="absolute bottom-32 left-1/4 w-16 h-16 border-2 border-blue-400 rounded-full"></div>
      </div>

      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 relative">
        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-16">
          {/* Left Side - Story Content */}
          <div className={`space-y-8 transform transition-all duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
            <div>
              <span className="inline-block px-4 py-2 bg-emerald-100 text-emerald-600 rounded-full text-sm font-medium mb-4">
                Our Story
              </span>
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Authentic Morocco,{' '}
                <span className="text-emerald-600">
                  Unforgettable Journeys
                </span>
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                Born in the heart of the Ourika Valley, our passion for sharing Morocco's hidden treasures
                began over 15 years ago. What started as a local guide service has grown into a trusted
                gateway to authentic Moroccan experiences.
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Heart className="w-6 h-6 text-emerald-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Local Heritage
                  </h3>
                  <p className="text-gray-600">
                    Deep-rooted connections with Berber communities and traditional ways of life.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Leaf className="w-6 h-6 text-orange-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Sustainable Tourism
                  </h3>
                  <p className="text-gray-600">
                    Supporting local communities while preserving the natural beauty of the Atlas Mountains.
                  </p>
                </div>
              </div>
            </div>

            {/* Founder Info */}
            <div className="bg-white/60 backdrop-blur-sm p-6 rounded-2xl border border-white/20">
              <div className="flex items-center gap-4">
                <img
                  src="https://i.pravatar.cc/80?img=12"
                  alt="Youssef Amellal, Founder of Ourika Travels"
                  className="w-16 h-16 rounded-full object-cover border-4 border-white"
                  width="64"
                  height="64"
                />
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">
                    Youssef Amellal
                  </h4>
                  <p className="text-emerald-600 font-medium">
                    Founder & Head Guide
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    "Every journey should touch your soul and connect you with our beautiful culture."
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Visual Content */}
          <div className={`space-y-6 transform transition-all duration-1000 delay-300 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'}`}>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="relative overflow-hidden rounded-2xl shadow-xl group">
                  <img
                    src="https://images.unsplash.com/photo-1717330550630-659d5d3f5f29?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw0fHxtb3VudGFpbnMlMjBsYW5kc2NhcGUlMjBtb3JvY2NvJTIwYXRsYXMlMjB2aWxsYWdlfGVufDB8MHx8b3JhbmdlfDE3NTYwNDk5ODB8MA&ixlib=rb-4.1.0&q=85"
                    alt="Atlas Mountains landscape at sunset - Photo by Marek Piwnicki on Unsplash"
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                    width="300"
                    height="192"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                </div>
                <div className="relative overflow-hidden rounded-2xl shadow-xl group">
                  <img
                    src="https://images.unsplash.com/photo-1747061460015-0f418c298113?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw3fHxjcmFmdHMlMjB0cmFkaXRpb25hbCUyMG1vcm9jY2FuJTIwYmVyYmVyJTIwYXJ0aXNhbnxlbnwwfDB8fHwxNzU2MDQ5OTc5fDA&ixlib=rb-4.1.0&q=85"
                    alt="Traditional Moroccan Berber crafts and colorful rugs - Photo by Nikita Pishchugin on Unsplash"
                    className="w-full h-32 object-cover group-hover:scale-110 transition-transform duration-500"
                    width="300"
                    height="128"
                  />
                </div>
              </div>
              <div className="space-y-4 mt-8">
                <div className="relative overflow-hidden rounded-2xl shadow-xl group">
                  <img
                    src="https://images.unsplash.com/photo-1646607028566-8d392aa72041?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw1fHxjcmFmdHMlMjB0cmFkaXRpb25hbCUyMG1vcm9jY2FuJTIwYmVyYmVyJTIwYXJ0aXNhbnxlbnwwfDB8fHwxNzU2MDQ5OTc5fDA&ixlib=rb-4.1.0&q=85"
                    alt="Moroccan artisan working on traditional crafts - Photo by Amara Saleh on Unsplash"
                    className="w-full h-40 object-cover group-hover:scale-110 transition-transform duration-500"
                    width="300"
                    height="160"
                  />
                </div>
                <div className="relative overflow-hidden rounded-2xl shadow-xl group">
                  <img
                    src="https://images.unsplash.com/photo-1633265512358-bb6052d48a0f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw3fHxndWlkZSUyMHRvdXJpc3RzJTIwaGlraW5nJTIwbW9yb2NjbyUyMG1vdW50YWluc3xlbnwwfDB8fHwxNzU2MDQ5OTc5fDA&ixlib=rb-4.1.0&q=85"
                    alt="Local guide leading tourists through desert landscape - Photo by Andrea Pasquali on Unsplash"
                    className="w-full h-40 object-cover group-hover:scale-110 transition-transform duration-500"
                    width="300"
                    height="160"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Section */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <div
                key={index}
                className={`text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 ${
                  isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div className="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <IconComponent className="w-6 h-6 text-emerald-600" />
                </div>
                <AnimatedCounter
                  end={stat.number}
                  suffix={stat.suffix}
                  duration={2000}
                />
                <div className="text-gray-600 font-medium mt-2">
                  {stat.label}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default AboutSection;