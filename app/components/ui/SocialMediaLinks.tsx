import { Facebook, Instagram, Twitter, Youtube, Mail, Phone } from 'lucide-react';

interface SocialLink {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

const SocialMediaLinks = () => {
  const socialLinks: SocialLink[] = [
    {
      name: 'Facebook',
      href: 'https://facebook.com/ourikatravels',
      icon: Facebook
    },
    {
      name: 'Instagram',
      href: 'https://instagram.com/ourikatravels',
      icon: Instagram
    },
    {
      name: 'Twitter',
      href: 'https://twitter.com/ourikatravels',
      icon: Twitter
    },
    {
      name: 'YouTube',
      href: 'https://youtube.com/ourikatravels',
      icon: Youtube
    }
  ];

  return (
    <div className="flex items-center gap-4">
      {socialLinks.map((link) => {
        const IconComponent = link.icon;
        return (
          <a
            key={link.name}
            href={link.href}
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-emerald-500 hover:text-white transition-all duration-300 transform hover:scale-110"
            aria-label={`Follow us on ${link.name}`}
          >
            <IconComponent className="w-5 h-5" />
          </a>
        );
      })}
    </div>
  );
};

export default SocialMediaLinks;