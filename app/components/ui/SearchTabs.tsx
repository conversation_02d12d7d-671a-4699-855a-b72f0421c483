"use client"
import { Search, Building2, MapPin, UtensilsCrossed, Plane, Home } from 'lucide-react';

interface SearchTab {
  id: string;
  label: string;
  icon: any;
}

interface SearchTabsProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const SearchTabs = ({ activeTab, onTabChange }: SearchTabsProps) => {
  const searchTabs: SearchTab[] = [
    { id: 'all', label: 'Search All', icon: Search },
    { id: 'hotels', label: 'Hotels', icon: Building2 },
    { id: 'experiences', label: 'Things to Do', icon: MapPin },
    { id: 'restaurants', label: 'Restaurants', icon: UtensilsCrossed },
    { id: 'flights', label: 'Flights', icon: Plane },
  ];

  return (
    <div className="flex flex-wrap justify-center gap-8 mb-0">
      {searchTabs.map((tab) => {
        const Icon = tab.icon;
        return (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex items-center space-x-2 px-1 py-3 text-base font-medium transition-all duration-200 relative ${
              activeTab === tab.id
                ? 'text-gray-900'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Icon className="w-5 h-5" />
            <span>{tab.label}</span>
            {activeTab === tab.id && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-900"></div>
            )}
          </button>
        );
      })}
    </div>
  );
};

export default SearchTabs;