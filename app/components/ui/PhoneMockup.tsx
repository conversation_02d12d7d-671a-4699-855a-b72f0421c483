const PhoneMockup = () => {
  return (
    <div className="bg-black rounded-3xl p-2 shadow-2xl transform rotate-12 hover:rotate-6 transition-transform duration-300">
      <div className="bg-white rounded-2xl overflow-hidden w-64 h-96 relative">
        <div className="bg-emerald-500 h-24 relative">
          <div className="absolute top-4 left-4 right-4">
            <div className="text-white text-sm font-medium">Rewards Balance</div>
            <div className="text-white text-2xl font-bold">$30.00</div>
          </div>
        </div>
        <div className="p-4 space-y-3">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div className="h-3 bg-gray-300 rounded w-20"></div>
              <div className="h-3 bg-emerald-400 rounded w-12"></div>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div className="h-3 bg-gray-300 rounded w-16"></div>
              <div className="h-3 bg-emerald-400 rounded w-10"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhoneMockup;