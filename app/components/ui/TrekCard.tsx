import StarRating from './StarRating';
import FavoriteButton from './FavoriteButton';
import PriceDisplay from './PriceDisplay';

interface Trek {
  id: number;
  type: string;
  title: string;
  duration: string;
  groupSize?: string;
  pickupAvailable: boolean;
  rating: number;
  reviewCount: number;
  originalPrice?: number;
  currentPrice: number;
  priceUnit: string;
  image: string;
}

interface TrekCardProps {
  trek: Trek;
  isFavorite: boolean;
  onToggleFavorite: (id: number) => void;
}

const TrekCard = ({ trek, isFavorite, onToggleFavorite }: TrekCardProps) => {
  return (
    <div className="group bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      {/* Image Container */}
      <div className="relative overflow-hidden">
        <img
          src={trek.image}
          alt={trek.title}
          className="w-full h-48 sm:h-52 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        
        <FavoriteButton 
          isFavorite={isFavorite}
          onToggle={() => onToggleFavorite(trek.id)}
        />

        {/* Trip Type Badge */}
        <div className="absolute top-3 left-3">
          <span className="bg-white text-gray-800 px-3 py-1 rounded-full text-xs font-medium shadow-md">
            {trek.type}
          </span>
        </div>
      </div>

      {/* Card Content */}
      <div className="p-4 sm:p-5">
        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-emerald-600 transition-colors duration-200">
          {trek.title}
        </h3>

        {/* Trip Details */}
        <div className="flex flex-wrap items-center gap-2 text-sm text-gray-600 mb-4">
          <span>{trek.duration}</span>
          {trek.groupSize && (
            <>
              <span>•</span>
              <span>{trek.groupSize}</span>
            </>
          )}
          {trek.pickupAvailable && (
            <>
              <span>•</span>
              <span className="text-emerald-600">Pickup available</span>
            </>
          )}
        </div>

        <StarRating rating={trek.rating} reviewCount={trek.reviewCount} />

        <PriceDisplay 
          originalPrice={trek.originalPrice}
          currentPrice={trek.currentPrice}
          priceUnit={trek.priceUnit}
        />
      </div>
    </div>
  );
};

export default TrekCard;