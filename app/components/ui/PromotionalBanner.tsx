import { Star } from 'lucide-react';
import FloatingIcons from './FloatingIcons';
import PhoneMockup from './PhoneMockup';

const PromotionalBanner = () => {
  return (
    <div className="w-full mx-auto">
      <div className="relative bg-gradient-to-r from-lime-400 via-green-400 to-emerald-400 rounded-3xl overflow-hidden shadow-2xl">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-black bg-opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                             radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                             radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 0%, transparent 50%)`
          }}></div>
        </div>

        <div className="relative grid lg:grid-cols-2 gap-8 items-center p-6 sm:p-8 lg:p-12">
          {/* Left Content */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1 bg-black bg-opacity-20 rounded-full px-3 py-1">
                <Star className="w-4 h-4 text-white" />
                <Star className="w-4 h-4 text-white" />
                <span className="text-white font-medium text-sm">Rewards</span>
              </div>
            </div>
            
            <div>
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-black mb-4">
                Introducing<br />
                OurikaTravels<br />
                Rewards
              </h2>
              
              <p className="text-lg sm:text-xl text-black font-medium mb-2">
                It pays to plan, book, and share.
              </p>
              <p className="text-base sm:text-lg text-black opacity-90 mb-6">
                Join now for $30 off Things to Do.
              </p>
            </div>
            
            <button className="bg-black hover:bg-gray-800 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg">
              Learn more
            </button>
          </div>

          {/* Right Content - Phone Mockup */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative">
              <FloatingIcons />
              <PhoneMockup />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionalBanner;