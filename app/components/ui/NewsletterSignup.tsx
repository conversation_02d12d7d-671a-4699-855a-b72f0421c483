'use client';

import { useState } from 'react';
import { Mail, Check } from 'lucide-react';

const NewsletterSignup = () => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setIsSubmitted(true);
      setEmail('');
      
      // Reset after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false);
      }, 3000);
    }, 1000);
  };

  return (
    <div className="bg-gradient-to-r from-emerald-50 to-blue-50 p-6 rounded-2xl">
      <div className="flex items-center gap-2 mb-3">
        <Mail className="w-5 h-5 text-emerald-600" />
        <h3 className="text-lg font-semibold text-gray-900">
          Stay Updated
        </h3>
      </div>

      <p className="text-gray-600 mb-4 text-sm">
        Get travel tips, exclusive offers, and Morocco insights delivered to your inbox.
      </p>

      {isSubmitted ? (
        <div className="flex items-center gap-2 text-emerald-600 py-3">
          <Check className="w-5 h-5" />
          <span className="font-medium">Thank you for subscribing!</span>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="flex gap-2">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
            required
          />
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 bg-emerald-500 hover:bg-emerald-600 disabled:bg-emerald-400 text-white rounded-lg transition-colors duration-200 text-sm font-medium min-w-[80px]"
          >
            {isLoading ? 'Joining...' : 'Subscribe'}
          </button>
        </form>
      )}
    </div>
  );
};

export default NewsletterSignup;