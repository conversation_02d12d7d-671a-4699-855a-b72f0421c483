'use client';

import { Star } from 'lucide-react';

interface ReviewCardProps {
  review: {
    id: number;
    name: string;
    location: string;
    avatar: string;
    rating: number;
    date: string;
    tour: string;
    review: string;
    images: string[];
    verified: boolean;
  };
}

const ReviewCard = ({ review }: ReviewCardProps) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="p-8 sm:p-12 lg:p-16">
      <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
        {/* Left Side - Review Text */}
        <div className="space-y-6">
          {/* Rating */}
          <div className="flex items-center gap-2">
            <div className="flex">{renderStars(review.rating)}</div>
            {review.verified && (
              <span className="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Verified Review
              </span>
            )}
          </div>

          {/* Review Text */}
          <blockquote className="text-xl sm:text-2xl text-gray-800 leading-relaxed font-medium">
            "{review.review}"
          </blockquote>

          {/* Tour Info */}
          <div className="bg-emerald-50 p-4 rounded-xl">
            <span className="text-emerald-600 font-medium">
              Tour: {review.tour}
            </span>
          </div>

          {/* Author Info */}
          <div className="flex items-center gap-4">
            <img
              src={review.avatar}
              alt={review.name}
              className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-lg"
            />
            <div>
              <h4 className="text-lg font-semibold text-gray-900">
                {review.name}
              </h4>
              <p className="text-gray-600">
                {review.location}
              </p>
              <p className="text-sm text-gray-500">
                {review.date}
              </p>
            </div>
          </div>
        </div>

        {/* Right Side - Review Images */}
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {review.images.map((image, index) => (
              <div
                key={index}
                className="relative overflow-hidden rounded-2xl shadow-lg aspect-square"
              >
                <img
                  src={image}
                  alt={`Review photo ${index + 1}`}
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                />
              </div>
            ))}
          </div>
          {review.images.length === 1 && (
            <div className="text-center p-8 bg-gray-100 rounded-2xl">
              <span className="text-gray-500">
                More photos in the full review
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReviewCard;