import { <PERSON>, MapPin, Star, User } from 'lucide-react';

const FloatingIcons = () => {
  return (
    <>
      <div className="absolute -top-4 -left-4 bg-white rounded-2xl p-3 shadow-lg animate-bounce" style={{ animationDelay: '0s' }}>
        <Gift className="w-6 h-6 text-emerald-500" />
      </div>
      <div className="absolute -top-8 right-8 bg-white rounded-2xl p-3 shadow-lg animate-bounce" style={{ animationDelay: '0.5s' }}>
        <MapPin className="w-6 h-6 text-blue-500" />
      </div>
      <div className="absolute -bottom-4 -left-8 bg-white rounded-2xl p-3 shadow-lg animate-bounce" style={{ animationDelay: '1s' }}>
        <Star className="w-6 h-6 text-yellow-500" />
      </div>
      <div className="absolute -bottom-8 right-4 bg-white rounded-2xl p-3 shadow-lg animate-bounce" style={{ animationDelay: '1.5s' }}>
        <User className="w-6 h-6 text-purple-500" />
      </div>
    </>
  );
};

export default FloatingIcons;