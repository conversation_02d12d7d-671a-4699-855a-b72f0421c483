"use client"

import Navbar from './components/layouts/Navbar';

export default function NavbarPreview() {
  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Content to test scrolling and navbar effects */}
      <div className="pt-20 px-4 max-w-4xl mx-auto">
        <div className="space-y-8">
          <section className="bg-white rounded-xl p-8 shadow-lg border border-gray-200">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Navbar Scroll Test
            </h1>
            <p className="text-gray-600 text-lg leading-relaxed">
              This page is designed to test the navbar scroll functionality.
              As you scroll down, the navbar should become more opaque and show a backdrop blur effect.
              <br /><br />
              <strong>Mobile Test:</strong> On mobile devices, test the hamburger menu functionality.
            </p>
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-700">
                <strong>Test Status:</strong> Scroll down to see the navbar background change!
              </p>
            </div>
          </section>

          <section className="bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl p-8 text-white">
            <h2 className="text-3xl font-bold mb-4">Ourika Valley Adventures</h2>
            <p className="text-emerald-100 text-lg leading-relaxed">
              Discover the breathtaking beauty of Morocco's hidden valley with our expert local guides. 
              Experience authentic Berber culture, stunning waterfalls, and majestic Atlas Mountain views.
            </p>
          </section>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Mountain Hiking Tours
              </h3>
              <p className="text-gray-600">
                Explore scenic trails through traditional Berber villages and witness stunning mountain landscapes.
              </p>
              <div className="mt-4 w-full h-2 bg-emerald-200 rounded-full">
                <div className="h-full w-3/4 bg-emerald-500 rounded-full"></div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Cultural Experiences
              </h3>
              <p className="text-gray-600">
                Immerse yourself in authentic Moroccan culture with local family visits and traditional meals.
              </p>
              <div className="mt-4 w-full h-2 bg-teal-200 rounded-full">
                <div className="h-full w-2/3 bg-teal-500 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Add more content to test scrolling effect */}
          <section className="bg-white rounded-xl p-8 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Navigation Features
            </h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span className="text-gray-700">Discover - Find amazing destinations</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span className="text-gray-700">Tours - Browse available tour packages</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span className="text-gray-700">Experiences - Unique cultural activities</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                <span className="text-gray-700">Rewards - Loyalty program benefits</span>
              </div>
            </div>
          </section>

          {/* Spacer content to test scroll effects */}
          <div className="h-96 bg-gradient-to-b from-emerald-50 to-teal-50 rounded-xl flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                Scroll to Test Navbar Effects
              </h3>
              <p className="text-gray-600">
                The navbar should become more opaque as you scroll down
              </p>
            </div>
          </div>

          <div className="h-96 bg-gradient-to-b from-amber-50 to-orange-50 rounded-xl flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                More Scroll Content
              </h3>
              <p className="text-gray-600">
                Keep scrolling to see the navbar backdrop blur effect
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}